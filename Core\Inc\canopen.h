#ifndef __CANOPEN_H
#define __CANOPEN_H

#include "can.h"

/* ========================================================================== */
/* 基本配置定义                                                               */
/* ========================================================================== */
#define CAN_REC_LEN             1024   // 定义缓冲区大小 1KB
#define FLASH_WRITE_SIZE        1024   // 每次写入Flash的大小(字节)
#define CANOPEN_NODE_ID         0x01   // 本节点ID
#define HEARTBEAT_INTERVAL_MS   1000   // 心跳间隔(毫秒)
#define RECEIVE_TIMEOUT_MS      2000   // 接收超时(毫秒)

/* ========================================================================== */
/* CANopen 通信对象ID定义                                                     */
/* ========================================================================== */
#define SDO_RX_COB_ID           (0x600 + CANOPEN_NODE_ID)  // 0x601 - SDO接收
#define SDO_TX_COB_ID           (0x580 + CANOPEN_NODE_ID)  // 0x581 - SDO发送
#define TPDO1_COB_ID            (0x180 + CANOPEN_NODE_ID)  // 0x181 - TPDO1发送
#define RPDO1_COB_ID            (0x200 + CANOPEN_NODE_ID)  // 0x201 - RPDO1接收
#define NMT_COB_ID              0x000                      // 0x000 - NMT管理
#define HEARTBEAT_COB_ID        (0x700 + CANOPEN_NODE_ID)  // 0x701 - 心跳

/* ========================================================================== */
/* CANopen SDO命令定义                                                        */
/* ========================================================================== */
#define SDO_CCS_DOWNLOAD_INIT   0x20   // 下载初始化 (0x20-0x2F)
#define SDO_CCS_DOWNLOAD_SEG    0x00   // 下载段 (0x00-0x1F)
#define SDO_CCS_UPLOAD_INIT     0x40   // 上传初始化 (0x40-0x4F)
#define SDO_CCS_UPLOAD_SEG      0x60   // 上传段 (0x60-0x7F)
#define SDO_SCS_DOWNLOAD_INIT   0x60   // 下载初始化响应
#define SDO_SCS_DOWNLOAD_SEG    0x20   // 下载段响应
#define SDO_SCS_UPLOAD_INIT     0x41   // 上传初始化响应
#define SDO_SCS_UPLOAD_SEG      0x00   // 上传段响应
#define SDO_ABORT               0x80   // SDO中止

/* ========================================================================== */
/* CANopen 对象字典索引定义                                                   */
/* ========================================================================== */
#define OD_DEVICE_TYPE          0x1000 // 设备类型
#define OD_ERROR_REGISTER       0x1001 // 错误寄存器
#define OD_MANUFACTURER_STATUS  0x1002 // 制造商状态
#define OD_IDENTITY_OBJECT      0x1018 // 身份对象
#define OD_BOOTLOADER_CMD       0x2000 // Bootloader命令
#define OD_BOOTLOADER_STATUS    0x2001 // Bootloader状态
#define OD_FIRMWARE_SIZE        0x2002 // 固件大小
#define OD_WRITE_PROGRESS       0x2003 // 写入进度

/* ========================================================================== */
/* Bootloader命令和状态定义                                                   */
/* ========================================================================== */
// Bootloader命令值
#define BL_CMD_ERASE_FLASH      0x01   // 擦除Flash
#define BL_CMD_JUMP_TO_APP      0x02   // 跳转到APP
#define BL_CMD_RESET_DEVICE     0x03   // 复位设备
#define BL_CMD_GET_VERSION      0x04   // 获取版本信息

// Bootloader状态值
#define BL_STATUS_IDLE          0x00   // 空闲
#define BL_STATUS_RECEIVING     0x01   // 接收固件中
#define BL_STATUS_PROGRAMMING   0x02   // 编程Flash中
#define BL_STATUS_COMPLETE      0x03   // 完成
#define BL_STATUS_ERROR         0x04   // 错误

/* ========================================================================== */
/* NMT状态定义                                                               */
/* ========================================================================== */
#define NMT_STATE_BOOT_UP       0x00   // 启动
#define NMT_STATE_STOPPED       0x04   // 停止
#define NMT_STATE_OPERATIONAL   0x05   // 运行
#define NMT_STATE_PRE_OPERATIONAL 0x7F // 预运行

/* ========================================================================== */
/* SDO错误代码定义                                                           */
/* ========================================================================== */
#define SDO_ABORT_TOGGLE_BIT    0x05030000  // 段传输切换位错误
#define SDO_ABORT_TIMEOUT       0x05040000  // SDO协议超时
#define SDO_ABORT_UNKNOWN_CMD   0x05040001  // 未知命令
#define SDO_ABORT_INVALID_BLOCK 0x05040002  // 无效块大小
#define SDO_ABORT_INVALID_SEQ   0x05040003  // 无效序列号
#define SDO_ABORT_ACCESS_FAILED 0x06010000  // 不支持访问对象
#define SDO_ABORT_WRITE_ONLY    0x06010001  // 试图读取只写对象
#define SDO_ABORT_READ_ONLY     0x06010002  // 试图写入只读对象
#define SDO_ABORT_OBJECT_NOT_EXIST 0x06020000 // 对象不存在
#define SDO_ABORT_SUBINDEX_NOT_EXIST 0x06090011 // 子索引不存在
#define SDO_ABORT_PARAM_LENGTH  0x06070010  // 参数长度错误
#define SDO_ABORT_PARAM_TOO_HIGH 0x06090031 // 参数值太高
#define SDO_ABORT_PARAM_TOO_LOW 0x06090032  // 参数值太低

/* ========================================================================== */
/* 兼容性定义（保持原有命令接口）                                             */
/* ========================================================================== */
#define CMD_CAN_ID              0x101  // 命令帧ID（兼容）
#define CMD_JUMP_TO_APP         0x01   // 跳转到APP命令（兼容）
#define CMD_ERASE_FLASH         0x02   // 擦除Flash命令（兼容）

/* ========================================================================== */
/* 数据结构定义                                                               */
/* ========================================================================== */

// SDO 传输状态结构体
typedef struct {
    uint8_t active;                         // SDO传输是否活跃
    uint16_t index;                         // 对象字典索引
    uint8_t subindex;                       // 子索引
    uint32_t size;                          // 数据大小
    uint8_t *data;                          // 数据指针
    uint32_t offset;                        // 当前偏移
    uint8_t toggle;                         // 段传输切换位
    uint32_t timeout;                       // 超时时间戳
} sdo_transfer_t;

/* ========================================================================== */
/* 全局变量声明                                                               */
/* ========================================================================== */

// CAN通信相关
extern CAN_TxHeaderTypeDef TxMeg;
extern CAN_RxHeaderTypeDef RxMeg;
extern uint8_t CAN_Rx_Data[8];
extern uint8_t CAN_Tx_Data[8];
extern uint16_t CAN_Baudrate;
extern uint8_t Error_register;

// CANopen状态变量
extern uint8_t canopen_node_state;         // CANopen节点状态
extern uint8_t bootloader_status;          // Bootloader状态
extern uint32_t firmware_total_size;       // 固件总大小
extern uint32_t write_progress_bytes;      // 写入进度（字节）
extern uint32_t last_heartbeat_time;       // 最后心跳时间

// SDO传输状态
extern sdo_transfer_t sdo_upload;          // SDO上传状态
extern sdo_transfer_t sdo_download;        // SDO下载状态

// 固件接收相关
extern uint8_t CAN_RX_BUF_A[CAN_REC_LEN];  // 缓冲区A
extern uint8_t CAN_RX_BUF_B[CAN_REC_LEN];  // 缓冲区B
extern uint8_t *current_rx_buffer;          // 当前接收缓冲区指针
extern uint8_t *current_flash_buffer;       // 当前Flash写入缓冲区指针
extern uint32_t current_flash_addr;         // 当前Flash写入地址
extern uint32_t total_received_bytes;       // 总接收字节数
extern uint32_t CAN1_RX_CNT;               // 接收缓冲区索引
extern uint8_t flash_write_pending;         // Flash写入挂起标志
extern uint32_t last_receive_time;          // 最后接收时间戳

// 命令标志
extern uint8_t jump_to_app_cmd;            // 跳转到APP命令标志
extern uint8_t erase_flash_cmd;            // 擦除Flash命令标志

/* ========================================================================== */
/* 函数声明 - 简化但功能完整的版本                                             */
/* ========================================================================== */

// ==================== CAN底层初始化函数 ====================
void CAN_User_Init(CAN_HandleTypeDef* hcan);

// ==================== 固件流式传输管理函数 ==================
void firmware_stream_init(void);           // 流式固件更新初始化
uint8_t check_receive_timeout(void);       // 检查接收超时
void reset_firmware_state(void);           // 重置固件接收状态
void firmware_process_data(uint8_t *data, uint8_t len); // 处理固件数据

// ==================== CANopen协议核心函数 ===================
void canopen_init(void);                   // CANopen初始化
void canopen_periodic_tasks(void);         // CANopen周期性任务

// ==================== CANopen通信函数 ======================
void canopen_send_heartbeat(void);         // 发送心跳
void canopen_send_tpdo1(void);             // 发送TPDO1（状态信息）

// ==================== SDO服务函数 ==========================
void sdo_process_download(uint8_t *data);  // 处理SDO下载
void sdo_process_upload(uint8_t *data);    // 处理SDO上载
void sdo_send_response(uint8_t *data);     // 发送SDO响应
void sdo_send_abort(uint16_t index, uint8_t subindex, uint32_t abort_code); // 发送SDO中止

// ==================== 对象字典访问函数 ======================
uint8_t od_read_entry(uint16_t index, uint8_t subindex, uint8_t *data, uint32_t *size); // 读取对象字典条目
uint8_t od_write_entry(uint16_t index, uint8_t subindex, uint8_t *data, uint32_t size); // 写入对象字典条目
uint8_t od_validate_access(uint16_t index, uint8_t subindex, uint8_t write_access); // 验证对象字典访问权限

// ==================== 兼容性函数 ===========================
void process_legacy_can_command(void);     // 处理传统CAN命令



#endif
