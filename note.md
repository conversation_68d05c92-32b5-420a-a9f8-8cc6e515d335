# CANopen Bootloader 项目

## 项目简介

基于 STM32F103 的 CANopen 协议 bootloader，支持通过 CAN 总线进行固件更新。

## 特性

### CANopen 协议支持
- **SDO（服务数据对象）**：用于关键命令控制
  - 跳转到APP
  - 擦除Flash
  - 设备复位
  - 参数配置
  
- **PDO（过程数据对象）**：用于状态监控
  - TPDO1：实时发送bootloader状态和进度
  - 心跳：节点状态监控
  
- **NMT（网络管理）**：节点状态控制
  - 支持运行/停止/预运行状态切换

### 固件传输
- **流式传输**：保持原有的高效流式传输方式
- **双缓冲**：1KB双缓冲区，边接收边写入
- **进度监控**：实时进度反馈
- **错误处理**：超时检测和错误恢复

### 兼容性
- **向后兼容**：支持原有的简单CAN命令
- **混合模式**：CANopen协议与传统协议并存

## 技术参数

- **MCU**: STM32F103
- **CAN波特率**: 1Mbps (可配置)
- **节点ID**: 0x01
- **应用程序地址**: 0x8005000
- **Bootloader大小**: 20KB
- **应用程序空间**: 108KB

## 文件结构

```
Core/
├── Inc/
│   ├── canopen.h          # CANopen协议定义
│   ├── can.h              # CAN驱动
│   ├── iap.h              # IAP功能
│   └── ...
├── Src/
│   ├── canopen.c          # CANopen协议实现
│   ├── can.c              # CAN驱动实现
│   ├── iap.c              # IAP功能实现
│   ├── main.c             # 主程序
│   └── ...
```

## 使用方法

详见：
- `CANopen_Usage_Guide.md` - 详细使用指南
- `CANopen_Test_Examples.md` - 测试示例

## 开发记录

### 2025-08-21
- ✅ 实现CANopen协议基础框架
- ✅ 添加SDO服务支持
- ✅ 实现TPDO1状态监控
- ✅ 集成心跳和Boot-up消息
- ✅ 实现对象字典访问
- ✅ 保持与原有协议的兼容性
- ✅ 添加NMT网络管理支持

### 待完成
- [ ] 添加更多对象字典条目
- [ ] 实现SDO分段传输
- [ ] 增加RPDO处理
- [ ] 添加配置参数持久化
- [ ] 完善错误处理机制

## 编译和烧录

使用 Keil MDK-ARM 或 STM32CubeIDE：

1. 打开项目文件
2. 编译项目
3. 烧录到STM32F103

## 测试验证

1. 硬件连接CAN总线
2. 使用CANopen工具或自定义测试程序
3. 参考测试示例进行验证

## 注意事项

1. 确保CAN总线正确终端匹配
2. 节点ID需要在网络中唯一
3. 固件传输时避免其他高优先级CAN消息干扰
4. 建议在测试环境中验证后再部署到生产环境