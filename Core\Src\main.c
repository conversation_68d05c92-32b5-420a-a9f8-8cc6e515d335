/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "can.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "canopen.h"
#include "iap.h"
#include "stmflash.h"
#include "sys.h"
#include "stdio.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
    uint8_t bit_new = 0;					//接收到程序标志
    uint8_t bit_10s = 0;
    uint8_t t = 0, clearflag = 0;
    uint8_t firmware_update_completed = 0;  //固件更新完成标志
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
  // 	__enable_irq();
  // __set_PRIMASK(0);
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART2_UART_Init();
  MX_CAN_Init();
  /* USER CODE BEGIN 2 */
  CAN_User_Init(&hcan);
  canopen_init(); // 初始化CANopen协议
  
  printf("Bootloader started\r\n");
  printf("CANopen SDO Commands (Node ID: 0x%02X):\r\n", CANOPEN_NODE_ID);
  printf("  SDO Write 0x2000.0 = 0x01 - Erase Flash\r\n");
  printf("  SDO Write 0x2000.0 = 0x02 - Jump to APP\r\n");
  printf("  SDO Read 0x2001.0 - Get Bootloader Status\r\n");
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
 
    // 处理擦除Flash命令
    if (erase_flash_cmd) {
        erase_flash_cmd = 0;
        bootloader_status = BL_STATUS_IDLE;
        printf("Processing erase Flash command...\r\n");
        iap_erase_app_area(); // 擦除APP区域
        printf("Flash erase completed. Ready for new firmware.\r\n");
    }
    
    // 处理CAN命令（跳转到APP）
    if (jump_to_app_cmd) {
        jump_to_app_cmd = 0;
        bootloader_status = BL_STATUS_COMPLETE;
        printf("Processing jump to APP command...\r\n");
        
        // 检查Flash中是否有有效的用户程序
        if (((*(volatile uint32_t*)(FLASH_APP1_ADDR + 4)) & 0xFF000000) == 0x08000000) {
            printf("Starting user application via CAN command...\r\n");
            iap_load_app(FLASH_APP1_ADDR); // 执行FLASH APP代码
        } else {
            printf("No valid user application found!\r\n");
        }
    }

    t++;
    HAL_Delay(10);
    if (t == 20)
    {
        t = 0;
        bit_10s++;
        
        // 每30秒显示一次状态信息
        if (bit_10s >= 30) {
            bit_10s = 0;
            if (((*(volatile uint32_t*)(FLASH_APP1_ADDR + 4)) & 0xFF000000) == 0x08000000) {
                printf("Bootloader running. Use SDO commands to erase/jump to APP.\r\n");
            } else {
                printf("Bootloader running. No valid user application found.\r\n");
            }
        }
        
        if (clearflag)
        {
            clearflag--;
            if (clearflag == 0)
                printf("Clear display!\r\n");
        }
    }

    }
    // HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);

  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
