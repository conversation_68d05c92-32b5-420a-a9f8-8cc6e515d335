#include "can.h"
#include "canopen.h"
#include "iap.h"
#include "string.h"
#include "stdio.h"

/* ========================================================================== */
/* 外部变量和句柄                                                             */
/* ========================================================================== */
extern CAN_HandleTypeDef hcan;             // CAN句柄外部声明

/* ========================================================================== */
/* 全局变量定义                                                               */
/* ========================================================================== */

// CAN通信相关变量
CAN_TxHeaderTypeDef TxMeg;                 // CAN发送消息头
CAN_RxHeaderTypeDef RxMeg;                 // CAN接收消息头
uint8_t CAN_Rx_Data[8];                    // CAN接收数据缓冲区
uint8_t CAN_Tx_Data[8];                    // CAN发送数据缓冲区
uint16_t CAN_Baudrate = 7;                 // CAN波特率设置，默认为7（1000kbit/s）
uint8_t Error_register = 0;                // 错误寄存器

// CANopen状态变量
uint8_t canopen_node_state = NMT_STATE_PRE_OPERATIONAL;  // CANopen节点状态
uint8_t bootloader_status = BL_STATUS_IDLE;              // Bootloader状态
uint32_t firmware_total_size = 0;                        // 固件总大小
uint32_t write_progress_bytes = 0;                       // 写入进度（字节）
uint32_t last_heartbeat_time = 0;                        // 最后心跳时间

// SDO传输状态
sdo_transfer_t sdo_upload = {0};                          // SDO上传状态
sdo_transfer_t sdo_download = {0};                        // SDO下载状态

// 固件接收相关
uint8_t CAN_RX_BUF_A[CAN_REC_LEN];         // 缓冲区A
uint8_t CAN_RX_BUF_B[CAN_REC_LEN];         // 缓冲区B

// 固件接收状态变量（使用传统全局变量方式）
uint8_t *current_rx_buffer = CAN_RX_BUF_A;     // 当前接收缓冲区指针
uint8_t *current_flash_buffer = CAN_RX_BUF_B;  // 当前Flash写入缓冲区指针
uint32_t current_flash_addr = 0;               // 当前Flash写入地址
uint32_t total_received_bytes = 0;             // 总接收字节数
uint32_t CAN1_RX_CNT = 0;                     // 接收缓冲区索引
uint8_t flash_write_pending = 0;               // Flash写入挂起标志
uint32_t last_receive_time = 0;                // 最后接收时间戳

// 命令标志
uint8_t jump_to_app_cmd = 0;               // 跳转到APP命令标志
uint8_t erase_flash_cmd = 0;               // 擦除Flash命令标志

/* ========================================================================== */
/* CAN底层初始化函数                                                          */
/* ========================================================================== */ 

/**
 * @brief CAN用户初始化函数
 * @param hcan CAN句柄指针
 * @retval None
 * 
 * 配置CAN波特率、过滤器，并启动CAN接收中断
 */
void CAN_User_Init(CAN_HandleTypeDef *hcan)
{
    CAN_FilterTypeDef sFilterConfig;
    HAL_StatusTypeDef HAL_Status;

    // 验证输入参数
    if (hcan == NULL) {
        Error_Handler();
        return;
    }

    // 初始化发送消息头
    TxMeg.IDE = CAN_ID_STD;
    TxMeg.RTR = CAN_RTR_DATA;

    // 根据波特率设置预分频值
    switch (CAN_Baudrate) {
        case 0: hcan->Init.Prescaler = 100; break;  // 20kbit/s
        case 1: hcan->Init.Prescaler = 40;  break;  // 50kbit/s
        case 2: hcan->Init.Prescaler = 20;  break;  // 100kbit/s
        case 3: hcan->Init.Prescaler = 16;  break;  // 125kbit/s
        case 4: hcan->Init.Prescaler = 8;   break;  // 250kbit/s
        case 5: hcan->Init.Prescaler = 4;   break;  // 500kbit/s
        case 7: hcan->Init.Prescaler = 2;   break;  // 1000kbit/s
        default: hcan->Init.Prescaler = 8;  break;  // 默认250kbit/s
    }

    // 初始化CAN
    if (HAL_CAN_Init(hcan) != HAL_OK) {
        Error_Handler();
        return;
    }

    // 配置CAN过滤器 - 接收所有消息
    sFilterConfig.FilterBank = 0;
    sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
    sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
    sFilterConfig.FilterIdHigh = 0x0000;
    sFilterConfig.FilterIdLow = 0x0000;
    sFilterConfig.FilterMaskIdHigh = 0x0000;  // 0表示接收所有
    sFilterConfig.FilterMaskIdLow = 0x0000;
    sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;
    sFilterConfig.FilterActivation = ENABLE;
    sFilterConfig.SlaveStartFilterBank = 0;

    HAL_Status = HAL_CAN_ConfigFilter(hcan, &sFilterConfig);
    if (HAL_Status != HAL_OK) {
        Error_Handler();
        return;
    }

    HAL_Status = HAL_CAN_Start(hcan);
    if (HAL_Status != HAL_OK) {
        Error_Handler();
        return;
    }

    HAL_Status = HAL_CAN_ActivateNotification(hcan, CAN_IT_RX_FIFO0_MSG_PENDING);
    if (HAL_Status != HAL_OK) {
        Error_Handler();
        return;
    }

    printf("CAN initialized: Baudrate index=%d\r\n", CAN_Baudrate);
}

/* ========================================================================== */
/* CAN接收中断处理函数                                                        */
/* ========================================================================== */

/**
 * @brief CAN接收FIFO0消息挂起中断回调函数
 * @param hcan CAN句柄指针
 * @retval None
 * 
 * 处理接收到的CAN消息，支持SDO命令和固件数据传输
 */
void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan)
{
    HAL_StatusTypeDef HAL_RetVal;
    HAL_RetVal = HAL_CAN_GetRxMessage(hcan, CAN_RX_FIFO0, &RxMeg, CAN_Rx_Data);
    if (HAL_RetVal != HAL_OK) {
        return;
    }

    // 处理SDO消息
    if (RxMeg.StdId == SDO_RX_COB_ID) {
        // printf("SDO RX: CCS=0x%02X\r\n", CAN_Rx_Data[0]);
        
        if (CAN_Rx_Data[0] & 0x80) {
            // SDO中止
            printf("SDO Abort received\r\n");
        } else if ((CAN_Rx_Data[0] & 0xE0) == SDO_CCS_DOWNLOAD_INIT) {
            // SDO下载初始化
            printf("Processing SDO Download Init\r\n");
            sdo_process_download(CAN_Rx_Data);
        } else if ((CAN_Rx_Data[0] & 0xE0) == SDO_CCS_UPLOAD_INIT) {
            // SDO上传初始化
            printf("Processing SDO Upload Init\r\n");
            sdo_process_upload(CAN_Rx_Data);
        } else {
            printf("Unsupported SDO command: 0x%02X\r\n", CAN_Rx_Data[0]);
        }
        return;
    }
    
    // 处理传统命令帧（兼容性）
    if (RxMeg.StdId == CMD_CAN_ID) {
        process_legacy_can_command();
        return;
    }
    
    // 处理固件数据
    firmware_process_data(CAN_Rx_Data, RxMeg.DLC);
}

/* ========================================================================== */
/* 固件流式传输管理函数                                                        */
/* ========================================================================== */

/**
 * @brief 固件流式更新初始化
 * @retval None
 * 
 * 初始化固件接收状态和缓冲区
 */
void firmware_stream_init(void)
{
    current_rx_buffer = CAN_RX_BUF_A;
    current_flash_buffer = CAN_RX_BUF_B;
    current_flash_addr = 0;
    total_received_bytes = 0;
    CAN1_RX_CNT = 0;
    flash_write_pending = 0;
    last_receive_time = 0;
    
    jump_to_app_cmd = 0;
    erase_flash_cmd = 0;
    
    // 重置CANopen状态
    bootloader_status = BL_STATUS_IDLE;
    write_progress_bytes = 0;
    firmware_total_size = 0;
    
    printf("Firmware stream initialized\r\n");
}

/**
 * @brief 检查接收超时
 * @retval 1-超时，0-未超时
 * 
 * 检查是否在规定时间内没有接收到新数据
 */
uint8_t check_receive_timeout(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 只有接收了足够的数据（至少64字节）才认为是固件传输
    if (total_received_bytes >= 64 && last_receive_time > 0) {
        return (current_time - last_receive_time) > RECEIVE_TIMEOUT_MS;
    }
    
    // 如果只收到少量数据，可能是其他CAN消息，10秒后清除
    if (total_received_bytes > 0 && 
        total_received_bytes < 64 && 
        last_receive_time > 0) {
        if ((current_time - last_receive_time) > 10000) {
            firmware_stream_init(); // 清除少量无关数据
        }
    }
    return 0;
}

/**
 * @brief 重置固件接收状态
 * @retval None
 */
void reset_firmware_state(void)
{
    firmware_stream_init();
}

/**
 * @brief 处理固件数据
 * @param data 接收到的数据
 * @param len 数据长度
 * @retval None
 * 
 * 将接收到的固件数据写入缓冲区，并处理缓冲区切换
 */
void firmware_process_data(uint8_t *data, uint8_t len)
{
    if (data == NULL || len == 0 || len > 8) {
        return;
    }
    
    // 更新最后接收时间戳
    last_receive_time = HAL_GetTick();
    
    // 如果是第一包数据，初始化Flash地址
    if (total_received_bytes == 0) {
        current_flash_addr = FLASH_APP1_ADDR;
        bootloader_status = BL_STATUS_RECEIVING;
        printf("Starting firmware reception...\r\n");
    }
    
    // 将数据写入当前接收缓冲区
    for (uint8_t i = 0; i < len; i++) {
        if (CAN1_RX_CNT < CAN_REC_LEN) {
            current_rx_buffer[CAN1_RX_CNT] = data[i];
            CAN1_RX_CNT++;
            total_received_bytes++;
            write_progress_bytes = total_received_bytes;
            
            // 缓冲区满了，需要切换缓冲区并标记写Flash
            if (CAN1_RX_CNT >= CAN_REC_LEN) {
                flash_write_pending = 1;
                bootloader_status = BL_STATUS_PROGRAMMING;
                CAN1_RX_CNT = 0;
                
                // 切换缓冲区
                if (current_rx_buffer == CAN_RX_BUF_A) {
                    current_rx_buffer = CAN_RX_BUF_B;
                    current_flash_buffer = CAN_RX_BUF_A;
                } else {
                    current_rx_buffer = CAN_RX_BUF_A;
                    current_flash_buffer = CAN_RX_BUF_B;
                }
                
                printf("Buffer switched, total received: %d bytes\r\n", 
                       total_received_bytes);
            }
        }
    }
}

/* ========================================================================== */
/* CANopen协议核心函数                                                        */
/* ========================================================================== */

/**
 * @brief CANopen初始化
 * @retval None
 * 
 * 初始化CANopen协议栈，设置节点状态和SDO传输状态
 */
void canopen_init(void)
{
    canopen_node_state = NMT_STATE_PRE_OPERATIONAL;
    bootloader_status = BL_STATUS_IDLE;
    firmware_total_size = 0;
    write_progress_bytes = 0;
    last_heartbeat_time = HAL_GetTick();
    
    // 初始化固件接收状态
    firmware_stream_init();
    
    printf("CANopen initialized - Node ID: 0x%02X\r\n", CANOPEN_NODE_ID);
    printf("SDO RX: 0x%03X, SDO TX: 0x%03X\r\n", SDO_RX_COB_ID, SDO_TX_COB_ID);
    printf("TPDO1: 0x%03X, RPDO1: 0x%03X\r\n", TPDO1_COB_ID, RPDO1_COB_ID);
    printf("Heartbeat: 0x%03X\r\n", HEARTBEAT_COB_ID);
}

/**
 * @brief CANopen周期性任务
 * @retval None
 * 
 * 执行CANopen协议的周期性任务，如心跳发送等
 */
void canopen_periodic_tasks(void)
{
    // 发送心跳
    canopen_send_heartbeat();
}

/* ========================================================================== */
/* CANopen通信函数                                                           */
/* ========================================================================== */

/**
 * @brief 发送心跳消息
 * @retval None
 * 
 * 定期发送心跳消息，通知网络节点状态
 */
void canopen_send_heartbeat(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 检查心跳间隔
    if (current_time - last_heartbeat_time >= HEARTBEAT_INTERVAL_MS) {
        TxMeg.StdId = HEARTBEAT_COB_ID;
        TxMeg.DLC = 1;
        TxMeg.IDE = CAN_ID_STD;
        TxMeg.RTR = CAN_RTR_DATA;
        
        CAN_Tx_Data[0] = canopen_node_state;
        
        uint32_t mailbox;
        if (HAL_CAN_AddTxMessage(&hcan, &TxMeg, CAN_Tx_Data, &mailbox) == HAL_OK) {
            last_heartbeat_time = current_time;
        }
    }
}

/**
 * @brief 发送TPDO1消息
 * @retval None
 * 
 * 发送TPDO1状态信息，包含Bootloader状态和进度
 */
void canopen_send_tpdo1(void)
{
    TxMeg.StdId = TPDO1_COB_ID;
    TxMeg.DLC = 8;
    TxMeg.IDE = CAN_ID_STD;
    TxMeg.RTR = CAN_RTR_DATA;
    
    // TPDO1数据映射：
    // Byte 0: Bootloader状态
    // Byte 1-4: 写入进度（字节数，小端序）
    // Byte 5: 错误寄存器
    // Byte 6-7: 保留
    CAN_Tx_Data[0] = bootloader_status;
    CAN_Tx_Data[1] = (uint8_t)(write_progress_bytes & 0xFF);
    CAN_Tx_Data[2] = (uint8_t)((write_progress_bytes >> 8) & 0xFF);
    CAN_Tx_Data[3] = (uint8_t)((write_progress_bytes >> 16) & 0xFF);
    CAN_Tx_Data[4] = (uint8_t)((write_progress_bytes >> 24) & 0xFF);
    CAN_Tx_Data[5] = Error_register;
    CAN_Tx_Data[6] = 0x00; // 保留
    CAN_Tx_Data[7] = 0x00; // 保留
    
    uint32_t mailbox;
    HAL_CAN_AddTxMessage(&hcan, &TxMeg, CAN_Tx_Data, &mailbox);
}


/* ========================================================================== */
/* SDO服务函数                                                               */
/* ========================================================================== */
/**
 * @brief SDO发送响应
 * @param data 要发送的8字节SDO数据
 * @retval None
 */
void sdo_send_response(uint8_t *data)
{
    if (data == NULL) {
        return;
    }
    
    TxMeg.StdId = SDO_TX_COB_ID;
    TxMeg.DLC = 8;
    TxMeg.IDE = CAN_ID_STD;
    TxMeg.RTR = CAN_RTR_DATA;
    
    memcpy(CAN_Tx_Data, data, 8);
    
    uint32_t mailbox;
    if (HAL_CAN_AddTxMessage(&hcan, &TxMeg, CAN_Tx_Data, &mailbox) != HAL_OK) {
        // update_error_register(0x10); // 通信错误
        printf("SDO response send failed\r\n");
    }
}

/**
 * @brief SDO发送中止消息
 * @param index 对象字典索引
 * @param subindex 子索引
 * @param abort_code 中止代码
 * @retval None
 */
void sdo_send_abort(uint16_t index, uint8_t subindex, uint32_t abort_code)
{
    uint8_t response[8];
    
    response[0] = SDO_ABORT;
    response[1] = (uint8_t)(index & 0xFF);
    response[2] = (uint8_t)((index >> 8) & 0xFF);
    response[3] = subindex;
    response[4] = (uint8_t)(abort_code & 0xFF);
    response[5] = (uint8_t)((abort_code >> 8) & 0xFF);
    response[6] = (uint8_t)((abort_code >> 16) & 0xFF);
    response[7] = (uint8_t)((abort_code >> 24) & 0xFF);
    
    sdo_send_response(response);
    
    printf("SDO Abort sent: Index=0x%04X, Sub=0x%02X, Code=0x%08X\r\n", 
           index, subindex, (unsigned int)abort_code);
}


/**
 * @brief 处理SDO下载 - 简化但功能完整的版本
 * @param data 接收到的SDO数据
 * @retval None
 * 
 * 处理SDO下载请求，支持bootloader命令和固件大小设置
 */
void sdo_process_download(uint8_t *data)
{
    if (data == NULL) {
        return;
    }
    
    uint8_t ccs = data[0];
    uint16_t index = (uint16_t)(data[1] | (data[2] << 8));
    uint8_t subindex = data[3];
    
    printf("SDO Download: CCS=0x%02X, Index=0x%04X, Sub=0x%02X\r\n", ccs, index, subindex);
    
    if ((ccs & 0xE0) == SDO_CCS_DOWNLOAD_INIT) {
        // 下载初始化
        uint8_t response[8];
        uint32_t data_size;
        uint8_t *obj_data = &data[4];
        
        // 解析数据大小
        if (ccs & 0x02) {
            // 快速传输 (e=1)
            if (ccs & 0x01) {
                // 指定数据大小 (s=1)
                data_size = 4 - ((ccs >> 2) & 0x03);
            } else {
                // 数据大小未指定 (s=0)
                data_size = 4;
            }
        } else {
            // 普通传输
            if (ccs & 0x01) {
                // s=1: 数据就在这帧中
                data_size = 4 - ((ccs >> 2) & 0x03);
            } else {
                // s=0: 分段传输，数据大小在字节4-7中
                data_size = (uint32_t)(data[4] | (data[5] << 8) | (data[6] << 16) | (data[7] << 24));
            }
        }
        
        // 验证对象字典访问权限
        if (!od_validate_access(index, subindex, 1)) {
            printf("SDO Access denied: 0x%04X:%02X\r\n", index, subindex);
            sdo_send_abort(index, subindex, SDO_ABORT_ACCESS_FAILED);
            return;
        }
        
        // 尝试写入对象字典
        if (od_write_entry(index, subindex, obj_data, data_size) == 0) {
            // 成功
            response[0] = SDO_SCS_DOWNLOAD_INIT;
            response[1] = data[1];
            response[2] = data[2];
            response[3] = data[3];
            response[4] = 0x00;
            response[5] = 0x00;
            response[6] = 0x00;
            response[7] = 0x00;
            
            sdo_send_response(response);
            printf("SDO Download success: 0x%04X:%02X\r\n", index, subindex);
        } else {
            // 失败
            printf("SDO Download failed: 0x%04X:%02X\r\n", index, subindex);
            sdo_send_abort(index, subindex, SDO_ABORT_ACCESS_FAILED);
        }
    } else {
        // 不支持分段传输
        sdo_send_abort(index, subindex, SDO_ABORT_UNKNOWN_CMD);
    }
}

/**
 * @brief 处理SDO上传 - 简化版本，只支持基本状态查询
 * @param data 接收到的SDO数据
 * @retval None
 * 
 * 简化的SDO上传处理，只支持基本状态查询
 */
void sdo_process_upload(uint8_t *data)
{
    if (data == NULL) {
        return;
    }
    
    uint8_t ccs = data[0];
    uint16_t index = (uint16_t)(data[1] | (data[2] << 8));
    uint8_t subindex = data[3];
    
    printf("SDO Upload: Index=0x%04X, Sub=0x%02X\r\n", index, subindex);
    
    // 只处理上传初始化命令
    if ((ccs & 0xE0) == SDO_CCS_UPLOAD_INIT) {
        uint8_t response[8];
        
        // 只支持读取bootloader状态
        if (index == OD_BOOTLOADER_STATUS && subindex == 0) {
            // 快速传输返回状态
            response[0] = SDO_SCS_UPLOAD_INIT | 0x02 | 0x01 | (3 << 2); // 1字节数据
            response[1] = data[1];
            response[2] = data[2];
            response[3] = data[3];
            response[4] = bootloader_status;
            response[5] = 0x00;
            response[6] = 0x00;
            response[7] = 0x00;
            
            sdo_send_response(response);
            printf("SDO Upload: Bootloader status = 0x%02X\r\n", bootloader_status);
        } else {
            // 不支持的对象
            printf("SDO Upload: Unsupported object: 0x%04X:%02X\r\n", index, subindex);
            sdo_send_abort(index, subindex, SDO_ABORT_OBJECT_NOT_EXIST);
        }
    } else {
        // 不支持的SDO命令
        printf("SDO Upload: Unsupported command type: 0x%02X\r\n", ccs);
        sdo_send_abort(index, subindex, SDO_ABORT_UNKNOWN_CMD);
    }
}

/* ========================================================================== */
/* 对象字典访问函数                                                           */
/* ========================================================================== */

/**
 * @brief 验证对象字典访问权限
 * @param index 对象字典索引
 * @param subindex 子索引
 * @param write_access 1-写访问，0-读访问
 * @retval 1-允许访问，0-拒绝访问
 */
uint8_t od_validate_access(uint16_t index, uint8_t subindex, uint8_t write_access)
{
    // 根据对象字典索引验证访问权限
    switch (index) {
        case OD_DEVICE_TYPE:
        case OD_ERROR_REGISTER:
        case OD_BOOTLOADER_STATUS:
        case OD_WRITE_PROGRESS:
            // 只读对象
            return (write_access == 0) ? 1 : 0;
            
        case OD_IDENTITY_OBJECT:
            // 身份对象只读
            return (write_access == 0) ? 1 : 0;
            
        case OD_BOOTLOADER_CMD:
        case OD_FIRMWARE_SIZE:
            // 可写对象
            return 1;
            
        default:
            // 未知对象
            return 0;
    }
}
/**
 * @brief 读取对象字典条目
 * @param index 对象字典索引
 * @param subindex 子索引
 * @param data 存储读取数据的缓冲区
 * @param size 返回数据大小的指针
 * @retval 0-成功，1-失败
 */
uint8_t od_read_entry(uint16_t index, uint8_t subindex, uint8_t *data, uint32_t *size)
{
    if (data == NULL || size == NULL) {
        return 1;
    }
    
    switch (index) {
        case OD_DEVICE_TYPE:
            if (subindex == 0) {
                *(uint32_t*)data = 0x00000000; // 未分类设备
                *size = 4;
                return 0;
            }
            break;
            
        case OD_ERROR_REGISTER:
            if (subindex == 0) {
                *data = Error_register;
                *size = 1;
                return 0;
            }
            break;
            
        case OD_BOOTLOADER_STATUS:
            if (subindex == 0) {
                *data = bootloader_status;
                *size = 1;
                return 0;
            }
            break;
            
        case OD_FIRMWARE_SIZE:
            if (subindex == 0) {
                *(uint32_t*)data = firmware_total_size;
                *size = 4;
                return 0;
            }
            break;
            
        case OD_WRITE_PROGRESS:
            if (subindex == 0) {
                *(uint32_t*)data = write_progress_bytes;
                *size = 4;
                return 0;
            }
            break;
            
        case OD_IDENTITY_OBJECT:
            switch (subindex) {
                case 0: // 条目数量
                    *data = 4;
                    *size = 1;
                    return 0;
                case 1: // Vendor ID
                    *(uint32_t*)data = 0x12345678;
                    *size = 4;
                    return 0;
                case 2: // Product code
                    *(uint32_t*)data = 0x00000001;
                    *size = 4;
                    return 0;
                case 3: // Revision number
                    *(uint32_t*)data = 0x00010000;
                    *size = 4;
                    return 0;
                case 4: // Serial number
                    *(uint32_t*)data = 0x00000001;
                    *size = 4;
                    return 0;
            }
            break;
    }
    
    return 1; // 对象不存在
}

/**
 * @brief 写入对象字典条目
 * @param index 对象字典索引
 * @param subindex 子索引
 * @param data 要写入的数据
 * @param size 数据大小
 * @retval 0-成功，1-失败
 */
uint8_t od_write_entry(uint16_t index, uint8_t subindex, uint8_t *data, uint32_t size)
{
    if (data == NULL || size == 0) {
        printf("od_write_entry: Invalid parameters\r\n");
        return 1;
    }
    
    printf("od_write_entry: Index=0x%04X, Sub=%02X, Size=%d\r\n", index, subindex, (int)size);
    
    switch (index) {
        case OD_BOOTLOADER_CMD:
            if (subindex == 0 && size == 1) {
                uint8_t cmd = *data;
                printf("Bootloader command received: 0x%02X\r\n", cmd);
                
                switch (cmd) {
                    case BL_CMD_ERASE_FLASH:
                        erase_flash_cmd = 1;
                        bootloader_status = BL_STATUS_IDLE;
                        printf("SDO: Erase Flash command\r\n");
                        break;
                    case BL_CMD_JUMP_TO_APP:
                        jump_to_app_cmd = 1;
                        printf("SDO: Jump to APP command\r\n");
                        break;
                    default:
                        printf("SDO: Unsupported command: 0x%02X\r\n", cmd);
                        return 1; // 不支持的命令
                }
                return 0;
            }
            break;
            
        case OD_FIRMWARE_SIZE:
            if (subindex == 0 && size == 4) {
                firmware_total_size = *(uint32_t*)data;
                printf("Firmware size set to: %d bytes\r\n", (int)firmware_total_size);
                return 0;
            }
            break;
    }
    
    return 1; // 对象不可写或不存在
}

/* ========================================================================== */
/* 兼容性函数                                                                */
/* ========================================================================== */

/**
 * @brief 处理传统CAN命令
 * @retval None
 * 
 * 处理原有的传统CAN命令格式，保持向后兼容性
 */
void process_legacy_can_command(void)
{
    if (RxMeg.DLC < 1) {
        return;
    }
    
    switch (CAN_Rx_Data[0]) {
        case CMD_JUMP_TO_APP:
            printf("Received legacy jump to APP command!\r\n");
            jump_to_app_cmd = 1;
            break;
            
        case CMD_ERASE_FLASH:
            printf("Received legacy erase Flash command!\r\n");
            erase_flash_cmd = 1;
            break;
            
        default:
            printf("Unknown legacy command: 0x%02X\r\n", CAN_Rx_Data[0]);
            break;
    }
}


